import Layout from '@/components/layout/Layout'
import BannerSlider from '@/components/features/BannerSlider'
import CategoriesGrid from '@/components/features/CategoriesGrid'
import FlashSale from '@/components/features/FlashSale'
import ProductGrid from '@/components/features/ProductGrid'

export default function Home() {
  return (
    <Layout>
      <div className="container-shopee py-6 space-y-6">
        {/* Main Banner and Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Banner Slider */}
          <div className="lg:col-span-3">
            <BannerSlider />
          </div>

          {/* Side Banners */}
          <div className="space-y-4">
            <div className="h-32 bg-gradient-to-r from-accent to-primary rounded-lg flex items-center justify-center">
              <div className="text-center text-white">
                <h3 className="font-bold">Shopee Mall</h3>
                <p className="text-sm opacity-90">Th<PERSON><PERSON><PERSON> hiệu ch<PERSON>h hãng</p>
              </div>
            </div>
            <div className="h-32 bg-gradient-to-r from-warning to-primary rounded-lg flex items-center justify-center">
              <div className="text-center text-white">
                <h3 className="font-bold">Miễn phí ship</h3>
                <p className="text-sm opacity-90">Đơn từ 0đ</p>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <CategoriesGrid />

        {/* Flash Sale */}
        <FlashSale />

        {/* Recommended Products */}
        <ProductGrid title="GỢI Ý HÔM NAY" />

        {/* Mall Products */}
        <ProductGrid title="SHOPEE MALL" />
      </div>
    </Layout>
  )
}
