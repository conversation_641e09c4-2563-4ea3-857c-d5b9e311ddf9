'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { cn } from '@/lib/utils'

interface Banner {
  id: string
  title: string
  image: string
  link?: string
}

// Mock data for banners
const mockBanners: Banner[] = [
  {
    id: '1',
    title: 'Flash Sale 12.12',
    image: '/api/placeholder/800/300',
    link: '/flash-sale',
  },
  {
    id: '2',
    title: 'Shopee Mall',
    image: '/api/placeholder/800/300',
    link: '/mall',
  },
  {
    id: '3',
    title: 'Miễn phí vận chuyển',
    image: '/api/placeholder/800/300',
    link: '/free-shipping',
  },
  {
    id: '4',
    title: 'Voucher giảm giá',
    image: '/api/placeholder/800/300',
    link: '/vouchers',
  },
]

const BannerSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % mockBanners.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + mockBanners.length) % mockBanners.length)
  }

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % mockBanners.length)
  }

  return (
    <div 
      className="relative w-full h-64 md:h-80 lg:h-96 overflow-hidden rounded-lg bg-gray-200"
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* Banner Images */}
      <div 
        className="flex transition-transform duration-500 ease-in-out h-full"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {mockBanners.map((banner) => (
          <div key={banner.id} className="w-full h-full flex-shrink-0 relative">
            <div className="w-full h-full bg-gradient-to-r from-primary to-accent flex items-center justify-center">
              <div className="text-center text-white">
                <h2 className="text-2xl md:text-4xl font-bold mb-4">{banner.title}</h2>
                <p className="text-lg md:text-xl opacity-90">
                  Khuyến mãi đặc biệt chỉ có tại Shopee
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white"
        onClick={goToPrevious}
      >
        <ChevronLeft className="h-6 w-6" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white"
        onClick={goToNext}
      >
        <ChevronRight className="h-6 w-6" />
      </Button>

      {/* Dots Indicator */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {mockBanners.map((_, index) => (
          <button
            key={index}
            className={cn(
              "w-3 h-3 rounded-full transition-colors",
              currentSlide === index ? "bg-white" : "bg-white/50"
            )}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>
    </div>
  )
}

export default BannerSlider
