{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/constants/index.ts"], "sourcesContent": ["// App Configuration\nexport const APP_CONFIG = {\n  name: '<PERSON><PERSON> Clone',\n  description: '<PERSON><PERSON> của trang thương mại điện tử Shopee',\n  url: 'http://localhost:3000',\n  version: '1.0.0',\n} as const\n\n// API Configuration\nexport const API_CONFIG = {\n  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',\n  timeout: 10000,\n} as const\n\n// Shopee Brand Colors\nexport const COLORS = {\n  primary: '#ee4d2d',\n  primaryHover: '#d73211',\n  secondary: '#f5f5f5',\n  accent: '#26aa99',\n  warning: '#ffbf00',\n  error: '#ff424f',\n  success: '#00c851',\n  text: {\n    primary: '#222222',\n    secondary: '#757575',\n    muted: '#999999',\n  },\n  border: '#e5e5e5',\n  background: '#f5f5f5',\n} as const\n\n// Navigation Menu Items\nexport const MAIN_CATEGORIES = [\n  { id: '1', name: '<PERSON>h<PERSON><PERSON> T<PERSON>', slug: 'thoi-trang-nam', icon: '👔' },\n  { id: '2', name: '<PERSON>h<PERSON><PERSON>', slug: 'thoi-trang-nu', icon: '👗' },\n  { id: '3', name: 'Điện Thoại & Phụ Kiện', slug: 'dien-thoai-phu-kien', icon: '📱' },\n  { id: '4', name: 'Máy Tính & Laptop', slug: 'may-tinh-laptop', icon: '💻' },\n  { id: '5', name: 'Máy Ảnh & Máy Quay Phim', slug: 'may-anh-may-quay', icon: '📷' },\n  { id: '6', name: 'Đồng Hồ', slug: 'dong-ho', icon: '⌚' },\n  { id: '7', name: 'Giày Dép Nam', slug: 'giay-dep-nam', icon: '👞' },\n  { id: '8', name: 'Giày Dép Nữ', slug: 'giay-dep-nu', icon: '👠' },\n  { id: '9', name: 'Túi Ví Nam', slug: 'tui-vi-nam', icon: '👜' },\n  { id: '10', name: 'Túi Ví Nữ', slug: 'tui-vi-nu', icon: '👛' },\n  { id: '11', name: 'Thiết Bị Điện Tử', slug: 'thiet-bi-dien-tu', icon: '🔌' },\n  { id: '12', name: 'Ô Tô & Xe Máy & Xe Đạp', slug: 'o-to-xe-may-xe-dap', icon: '🚗' },\n  { id: '13', name: 'Nhà Cửa & Đời Sống', slug: 'nha-cua-doi-song', icon: '🏠' },\n  { id: '14', name: 'Sắc Đẹp', slug: 'sac-dep', icon: '💄' },\n  { id: '15', name: 'Sức Khỏe', slug: 'suc-khoe', icon: '💊' },\n  { id: '16', name: 'Thể Thao & Du Lịch', slug: 'the-thao-du-lich', icon: '⚽' },\n  { id: '17', name: 'Bách Hóa Online', slug: 'bach-hoa-online', icon: '🛒' },\n  { id: '18', name: 'Nhà Sách Online', slug: 'nha-sach-online', icon: '📚' },\n] as const\n\n// Quick Links\nexport const QUICK_LINKS = [\n  { name: 'Kênh Người Bán', href: '/seller' },\n  { name: 'Trở thành Người bán Shopee', href: '/seller/register' },\n  { name: 'Tải ứng dụng', href: '/download' },\n  { name: 'Kết nối', href: '/connect' },\n] as const\n\n// Footer Links\nexport const FOOTER_LINKS = {\n  customerCare: [\n    { name: 'Trung Tâm Trợ Giúp', href: '/help' },\n    { name: 'Shopee Blog', href: '/blog' },\n    { name: 'Shopee Mall', href: '/mall' },\n    { name: 'Hướng Dẫn Mua Hàng', href: '/guide/buy' },\n    { name: 'Hướng Dẫn Bán Hàng', href: '/guide/sell' },\n    { name: 'Thanh Toán', href: '/payment' },\n    { name: 'Shopee Xu', href: '/coins' },\n    { name: 'Vận Chuyển', href: '/shipping' },\n    { name: 'Trả Hàng & Hoàn Tiền', href: '/returns' },\n    { name: 'Chăm Sóc Khách Hàng', href: '/support' },\n    { name: 'Chính Sách Bảo Hành', href: '/warranty' },\n  ],\n  about: [\n    { name: 'Giới Thiệu Về Shopee Việt Nam', href: '/about' },\n    { name: 'Tuyển Dụng', href: '/careers' },\n    { name: 'Điều Khoản Shopee', href: '/terms' },\n    { name: 'Chính Sách Bảo Mật', href: '/privacy' },\n    { name: 'Chính Hãng', href: '/authentic' },\n    { name: 'Kênh Người Bán', href: '/seller' },\n    { name: 'Flash Sales', href: '/flash-sales' },\n    { name: 'Chương Trình Tiếp Thị Liên Kết Shopee', href: '/affiliate' },\n    { name: 'Liên Hệ Với Truyền Thông', href: '/media' },\n  ],\n  payment: [\n    { name: 'Visa', icon: '/icons/visa.svg' },\n    { name: 'Mastercard', icon: '/icons/mastercard.svg' },\n    { name: 'JCB', icon: '/icons/jcb.svg' },\n    { name: 'American Express', icon: '/icons/amex.svg' },\n    { name: 'COD', icon: '/icons/cod.svg' },\n    { name: 'Shopee Pay', icon: '/icons/shopeepay.svg' },\n    { name: 'SPayLater', icon: '/icons/spaylater.svg' },\n  ],\n  logistics: [\n    { name: 'Shopee Express', icon: '/icons/shopee-express.svg' },\n    { name: 'Giao Hàng Tiết Kiệm', icon: '/icons/ghtk.svg' },\n    { name: 'GHN', icon: '/icons/ghn.svg' },\n    { name: 'Viettel Post', icon: '/icons/viettel-post.svg' },\n    { name: 'Vietnam Post', icon: '/icons/vietnam-post.svg' },\n    { name: 'J&T Express', icon: '/icons/jt-express.svg' },\n    { name: 'Grab Express', icon: '/icons/grab-express.svg' },\n    { name: 'Ninja Van', icon: '/icons/ninja-van.svg' },\n    { name: 'Best Express', icon: '/icons/best-express.svg' },\n    { name: 'Be', icon: '/icons/be.svg' },\n  ],\n} as const\n\n// Social Media Links\nexport const SOCIAL_LINKS = [\n  { name: 'Facebook', href: 'https://facebook.com/ShopeeVN', icon: 'facebook' },\n  { name: 'Instagram', href: 'https://instagram.com/shopee_vn', icon: 'instagram' },\n  { name: 'LinkedIn', href: 'https://linkedin.com/company/shopee', icon: 'linkedin' },\n] as const\n\n// Pagination\nexport const PAGINATION = {\n  defaultLimit: 20,\n  maxLimit: 100,\n} as const\n\n// Image Sizes\nexport const IMAGE_SIZES = {\n  thumbnail: { width: 100, height: 100 },\n  small: { width: 200, height: 200 },\n  medium: { width: 400, height: 400 },\n  large: { width: 800, height: 800 },\n  banner: { width: 1200, height: 400 },\n} as const\n\n// Breakpoints (matching Tailwind CSS)\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  cart: 'shopee_cart',\n  user: 'shopee_user',\n  wishlist: 'shopee_wishlist',\n  recentlyViewed: 'shopee_recently_viewed',\n  searchHistory: 'shopee_search_history',\n} as const\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  network: 'Lỗi kết nối mạng. Vui lòng thử lại.',\n  server: 'Lỗi máy chủ. Vui lòng thử lại sau.',\n  notFound: 'Không tìm thấy trang.',\n  unauthorized: 'Bạn cần đăng nhập để thực hiện hành động này.',\n  forbidden: 'Bạn không có quyền truy cập.',\n  validation: 'Dữ liệu không hợp lệ.',\n  unknown: 'Đã xảy ra lỗi không xác định.',\n} as const\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;;;;;;AACb,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;AACX;AAGO,MAAM,aAAa;IACxB,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;IAC5C,SAAS;AACX;AAGO,MAAM,SAAS;IACpB,SAAS;IACT,cAAc;IACd,WAAW;IACX,QAAQ;IACR,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;QACJ,SAAS;QACT,WAAW;QACX,OAAO;IACT;IACA,QAAQ;IACR,YAAY;AACd;AAGO,MAAM,kBAAkB;IAC7B;QAAE,IAAI;QAAK,MAAM;QAAkB,MAAM;QAAkB,MAAM;IAAK;IACtE;QAAE,IAAI;QAAK,MAAM;QAAiB,MAAM;QAAiB,MAAM;IAAK;IACpE;QAAE,IAAI;QAAK,MAAM;QAAyB,MAAM;QAAuB,MAAM;IAAK;IAClF;QAAE,IAAI;QAAK,MAAM;QAAqB,MAAM;QAAmB,MAAM;IAAK;IAC1E;QAAE,IAAI;QAAK,MAAM;QAA2B,MAAM;QAAoB,MAAM;IAAK;IACjF;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;QAAW,MAAM;IAAI;IACvD;QAAE,IAAI;QAAK,MAAM;QAAgB,MAAM;QAAgB,MAAM;IAAK;IAClE;QAAE,IAAI;QAAK,MAAM;QAAe,MAAM;QAAe,MAAM;IAAK;IAChE;QAAE,IAAI;QAAK,MAAM;QAAc,MAAM;QAAc,MAAM;IAAK;IAC9D;QAAE,IAAI;QAAM,MAAM;QAAa,MAAM;QAAa,MAAM;IAAK;IAC7D;QAAE,IAAI;QAAM,MAAM;QAAoB,MAAM;QAAoB,MAAM;IAAK;IAC3E;QAAE,IAAI;QAAM,MAAM;QAA0B,MAAM;QAAsB,MAAM;IAAK;IACnF;QAAE,IAAI;QAAM,MAAM;QAAsB,MAAM;QAAoB,MAAM;IAAK;IAC7E;QAAE,IAAI;QAAM,MAAM;QAAW,MAAM;QAAW,MAAM;IAAK;IACzD;QAAE,IAAI;QAAM,MAAM;QAAY,MAAM;QAAY,MAAM;IAAK;IAC3D;QAAE,IAAI;QAAM,MAAM;QAAsB,MAAM;QAAoB,MAAM;IAAI;IAC5E;QAAE,IAAI;QAAM,MAAM;QAAmB,MAAM;QAAmB,MAAM;IAAK;IACzE;QAAE,IAAI;QAAM,MAAM;QAAmB,MAAM;QAAmB,MAAM;IAAK;CAC1E;AAGM,MAAM,cAAc;IACzB;QAAE,MAAM;QAAkB,MAAM;IAAU;IAC1C;QAAE,MAAM;QAA8B,MAAM;IAAmB;IAC/D;QAAE,MAAM;QAAgB,MAAM;IAAY;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAGM,MAAM,eAAe;IAC1B,cAAc;QACZ;YAAE,MAAM;YAAsB,MAAM;QAAQ;QAC5C;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAsB,MAAM;QAAc;QAClD;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAc,MAAM;QAAY;QACxC;YAAE,MAAM;YAAwB,MAAM;QAAW;QACjD;YAAE,MAAM;YAAuB,MAAM;QAAW;QAChD;YAAE,MAAM;YAAuB,MAAM;QAAY;KAClD;IACD,OAAO;QACL;YAAE,MAAM;YAAiC,MAAM;QAAS;QACxD;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAqB,MAAM;QAAS;QAC5C;YAAE,MAAM;YAAsB,MAAM;QAAW;QAC/C;YAAE,MAAM;YAAc,MAAM;QAAa;QACzC;YAAE,MAAM;YAAkB,MAAM;QAAU;QAC1C;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAyC,MAAM;QAAa;QACpE;YAAE,MAAM;YAA4B,MAAM;QAAS;KACpD;IACD,SAAS;QACP;YAAE,MAAM;YAAQ,MAAM;QAAkB;QACxC;YAAE,MAAM;YAAc,MAAM;QAAwB;QACpD;YAAE,MAAM;YAAO,MAAM;QAAiB;QACtC;YAAE,MAAM;YAAoB,MAAM;QAAkB;QACpD;YAAE,MAAM;YAAO,MAAM;QAAiB;QACtC;YAAE,MAAM;YAAc,MAAM;QAAuB;QACnD;YAAE,MAAM;YAAa,MAAM;QAAuB;KACnD;IACD,WAAW;QACT;YAAE,MAAM;YAAkB,MAAM;QAA4B;QAC5D;YAAE,MAAM;YAAuB,MAAM;QAAkB;QACvD;YAAE,MAAM;YAAO,MAAM;QAAiB;QACtC;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAe,MAAM;QAAwB;QACrD;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAa,MAAM;QAAuB;QAClD;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAM,MAAM;QAAgB;KACrC;AACH;AAGO,MAAM,eAAe;IAC1B;QAAE,MAAM;QAAY,MAAM;QAAiC,MAAM;IAAW;IAC5E;QAAE,MAAM;QAAa,MAAM;QAAmC,MAAM;IAAY;IAChF;QAAE,MAAM;QAAY,MAAM;QAAuC,MAAM;IAAW;CACnF;AAGM,MAAM,aAAa;IACxB,cAAc;IACd,UAAU;AACZ;AAGO,MAAM,cAAc;IACzB,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,OAAO;QAAE,OAAO;QAAK,QAAQ;IAAI;IACjC,QAAQ;QAAE,OAAO;QAAK,QAAQ;IAAI;IAClC,OAAO;QAAE,OAAO;QAAK,QAAQ;IAAI;IACjC,QAAQ;QAAE,OAAO;QAAM,QAAQ;IAAI;AACrC;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,eAAe;IAC1B,MAAM;IACN,MAAM;IACN,UAAU;IACV,gBAAgB;IAChB,eAAe;AACjB;AAGO,MAAM,iBAAiB;IAC5B,SAAS;IACT,QAAQ;IACR,UAAU;IACV,cAAc;IACd,WAAW;IACX,YAAY;IACZ,SAAS;AACX", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport { APP_CONFIG } from \"@/constants\";\n\nexport const metadata: Metadata = {\n  title: APP_CONFIG.name,\n  description: APP_CONFIG.description,\n  keywords: [\"shopee\", \"thương mại điện tử\", \"mua sắm online\", \"ecommerce\"],\n  authors: [{ name: \"Shopee Clone Team\" }],\n  robots: \"index, follow\",\n  openGraph: {\n    title: APP_CONFIG.name,\n    description: APP_CONFIG.description,\n    url: APP_CONFIG.url,\n    siteName: APP_CONFIG.name,\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: APP_CONFIG.name,\n    description: APP_CONFIG.description,\n  },\n};\n\nexport const viewport = {\n  width: \"device-width\",\n  initialScale: 1,\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"vi\">\n      <body className=\"antialiased\">\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO,yHAAA,CAAA,aAAU,CAAC,IAAI;IACtB,aAAa,yHAAA,CAAA,aAAU,CAAC,WAAW;IACnC,UAAU;QAAC;QAAU;QAAsB;QAAkB;KAAY;IACzE,SAAS;QAAC;YAAE,MAAM;QAAoB;KAAE;IACxC,QAAQ;IACR,WAAW;QACT,OAAO,yHAAA,CAAA,aAAU,CAAC,IAAI;QACtB,aAAa,yHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,KAAK,yHAAA,CAAA,aAAU,CAAC,GAAG;QACnB,UAAU,yHAAA,CAAA,aAAU,CAAC,IAAI;QACzB,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO,yHAAA,CAAA,aAAU,CAAC,IAAI;QACtB,aAAa,yHAAA,CAAA,aAAU,CAAC,WAAW;IACrC;AACF;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;sBACb;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}