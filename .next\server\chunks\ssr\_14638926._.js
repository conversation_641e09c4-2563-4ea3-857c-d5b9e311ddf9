module.exports = {

"[project]/src/constants/index.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// App Configuration
__turbopack_context__.s({
    "API_CONFIG": ()=>API_CONFIG,
    "APP_CONFIG": ()=>APP_CONFIG,
    "BREAKPOINTS": ()=>BREAKPOINTS,
    "COLORS": ()=>COLORS,
    "ERROR_MESSAGES": ()=>ERROR_MESSAGES,
    "FOOTER_LINKS": ()=>FOOTER_LINKS,
    "IMAGE_SIZES": ()=>IMAGE_SIZES,
    "MAIN_CATEGORIES": ()=>MAIN_CATEGORIES,
    "PAGINATION": ()=>PAGINATION,
    "QUICK_LINKS": ()=>QUICK_LINKS,
    "SOCIAL_LINKS": ()=>SOCIAL_LINKS,
    "STORAGE_KEYS": ()=>STORAGE_KEYS
});
const APP_CONFIG = {
    name: '<PERSON><PERSON>lone',
    description: '<PERSON><PERSON> của trang thương mại đi<PERSON>n tử Shopee',
    url: 'http://localhost:3000',
    version: '1.0.0'
};
const API_CONFIG = {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
    timeout: 10000
};
const COLORS = {
    primary: '#ee4d2d',
    primaryHover: '#d73211',
    secondary: '#f5f5f5',
    accent: '#26aa99',
    warning: '#ffbf00',
    error: '#ff424f',
    success: '#00c851',
    text: {
        primary: '#222222',
        secondary: '#757575',
        muted: '#999999'
    },
    border: '#e5e5e5',
    background: '#f5f5f5'
};
const MAIN_CATEGORIES = [
    {
        id: '1',
        name: 'Thời Trang Nam',
        slug: 'thoi-trang-nam',
        icon: '👔'
    },
    {
        id: '2',
        name: 'Thời Trang Nữ',
        slug: 'thoi-trang-nu',
        icon: '👗'
    },
    {
        id: '3',
        name: 'Điện Thoại & Phụ Kiện',
        slug: 'dien-thoai-phu-kien',
        icon: '📱'
    },
    {
        id: '4',
        name: 'Máy Tính & Laptop',
        slug: 'may-tinh-laptop',
        icon: '💻'
    },
    {
        id: '5',
        name: 'Máy Ảnh & Máy Quay Phim',
        slug: 'may-anh-may-quay',
        icon: '📷'
    },
    {
        id: '6',
        name: 'Đồng Hồ',
        slug: 'dong-ho',
        icon: '⌚'
    },
    {
        id: '7',
        name: 'Giày Dép Nam',
        slug: 'giay-dep-nam',
        icon: '👞'
    },
    {
        id: '8',
        name: 'Giày Dép Nữ',
        slug: 'giay-dep-nu',
        icon: '👠'
    },
    {
        id: '9',
        name: 'Túi Ví Nam',
        slug: 'tui-vi-nam',
        icon: '👜'
    },
    {
        id: '10',
        name: 'Túi Ví Nữ',
        slug: 'tui-vi-nu',
        icon: '👛'
    },
    {
        id: '11',
        name: 'Thiết Bị Điện Tử',
        slug: 'thiet-bi-dien-tu',
        icon: '🔌'
    },
    {
        id: '12',
        name: 'Ô Tô & Xe Máy & Xe Đạp',
        slug: 'o-to-xe-may-xe-dap',
        icon: '🚗'
    },
    {
        id: '13',
        name: 'Nhà Cửa & Đời Sống',
        slug: 'nha-cua-doi-song',
        icon: '🏠'
    },
    {
        id: '14',
        name: 'Sắc Đẹp',
        slug: 'sac-dep',
        icon: '💄'
    },
    {
        id: '15',
        name: 'Sức Khỏe',
        slug: 'suc-khoe',
        icon: '💊'
    },
    {
        id: '16',
        name: 'Thể Thao & Du Lịch',
        slug: 'the-thao-du-lich',
        icon: '⚽'
    },
    {
        id: '17',
        name: 'Bách Hóa Online',
        slug: 'bach-hoa-online',
        icon: '🛒'
    },
    {
        id: '18',
        name: 'Nhà Sách Online',
        slug: 'nha-sach-online',
        icon: '📚'
    }
];
const QUICK_LINKS = [
    {
        name: 'Kênh Người Bán',
        href: '/seller'
    },
    {
        name: 'Trở thành Người bán Shopee',
        href: '/seller/register'
    },
    {
        name: 'Tải ứng dụng',
        href: '/download'
    },
    {
        name: 'Kết nối',
        href: '/connect'
    }
];
const FOOTER_LINKS = {
    customerCare: [
        {
            name: 'Trung Tâm Trợ Giúp',
            href: '/help'
        },
        {
            name: 'Shopee Blog',
            href: '/blog'
        },
        {
            name: 'Shopee Mall',
            href: '/mall'
        },
        {
            name: 'Hướng Dẫn Mua Hàng',
            href: '/guide/buy'
        },
        {
            name: 'Hướng Dẫn Bán Hàng',
            href: '/guide/sell'
        },
        {
            name: 'Thanh Toán',
            href: '/payment'
        },
        {
            name: 'Shopee Xu',
            href: '/coins'
        },
        {
            name: 'Vận Chuyển',
            href: '/shipping'
        },
        {
            name: 'Trả Hàng & Hoàn Tiền',
            href: '/returns'
        },
        {
            name: 'Chăm Sóc Khách Hàng',
            href: '/support'
        },
        {
            name: 'Chính Sách Bảo Hành',
            href: '/warranty'
        }
    ],
    about: [
        {
            name: 'Giới Thiệu Về Shopee Việt Nam',
            href: '/about'
        },
        {
            name: 'Tuyển Dụng',
            href: '/careers'
        },
        {
            name: 'Điều Khoản Shopee',
            href: '/terms'
        },
        {
            name: 'Chính Sách Bảo Mật',
            href: '/privacy'
        },
        {
            name: 'Chính Hãng',
            href: '/authentic'
        },
        {
            name: 'Kênh Người Bán',
            href: '/seller'
        },
        {
            name: 'Flash Sales',
            href: '/flash-sales'
        },
        {
            name: 'Chương Trình Tiếp Thị Liên Kết Shopee',
            href: '/affiliate'
        },
        {
            name: 'Liên Hệ Với Truyền Thông',
            href: '/media'
        }
    ],
    payment: [
        {
            name: 'Visa',
            icon: '/icons/visa.svg'
        },
        {
            name: 'Mastercard',
            icon: '/icons/mastercard.svg'
        },
        {
            name: 'JCB',
            icon: '/icons/jcb.svg'
        },
        {
            name: 'American Express',
            icon: '/icons/amex.svg'
        },
        {
            name: 'COD',
            icon: '/icons/cod.svg'
        },
        {
            name: 'Shopee Pay',
            icon: '/icons/shopeepay.svg'
        },
        {
            name: 'SPayLater',
            icon: '/icons/spaylater.svg'
        }
    ],
    logistics: [
        {
            name: 'Shopee Express',
            icon: '/icons/shopee-express.svg'
        },
        {
            name: 'Giao Hàng Tiết Kiệm',
            icon: '/icons/ghtk.svg'
        },
        {
            name: 'GHN',
            icon: '/icons/ghn.svg'
        },
        {
            name: 'Viettel Post',
            icon: '/icons/viettel-post.svg'
        },
        {
            name: 'Vietnam Post',
            icon: '/icons/vietnam-post.svg'
        },
        {
            name: 'J&T Express',
            icon: '/icons/jt-express.svg'
        },
        {
            name: 'Grab Express',
            icon: '/icons/grab-express.svg'
        },
        {
            name: 'Ninja Van',
            icon: '/icons/ninja-van.svg'
        },
        {
            name: 'Best Express',
            icon: '/icons/best-express.svg'
        },
        {
            name: 'Be',
            icon: '/icons/be.svg'
        }
    ]
};
const SOCIAL_LINKS = [
    {
        name: 'Facebook',
        href: 'https://facebook.com/ShopeeVN',
        icon: 'facebook'
    },
    {
        name: 'Instagram',
        href: 'https://instagram.com/shopee_vn',
        icon: 'instagram'
    },
    {
        name: 'LinkedIn',
        href: 'https://linkedin.com/company/shopee',
        icon: 'linkedin'
    }
];
const PAGINATION = {
    defaultLimit: 20,
    maxLimit: 100
};
const IMAGE_SIZES = {
    thumbnail: {
        width: 100,
        height: 100
    },
    small: {
        width: 200,
        height: 200
    },
    medium: {
        width: 400,
        height: 400
    },
    large: {
        width: 800,
        height: 800
    },
    banner: {
        width: 1200,
        height: 400
    }
};
const BREAKPOINTS = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536
};
const STORAGE_KEYS = {
    cart: 'shopee_cart',
    user: 'shopee_user',
    wishlist: 'shopee_wishlist',
    recentlyViewed: 'shopee_recently_viewed',
    searchHistory: 'shopee_search_history'
};
const ERROR_MESSAGES = {
    network: 'Lỗi kết nối mạng. Vui lòng thử lại.',
    server: 'Lỗi máy chủ. Vui lòng thử lại sau.',
    notFound: 'Không tìm thấy trang.',
    unauthorized: 'Bạn cần đăng nhập để thực hiện hành động này.',
    forbidden: 'Bạn không có quyền truy cập.',
    validation: 'Dữ liệu không hợp lệ.',
    unknown: 'Đã xảy ra lỗi không xác định.'
};
}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>RootLayout,
    "metadata": ()=>metadata,
    "viewport": ()=>viewport
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/index.ts [app-rsc] (ecmascript)");
;
;
;
const metadata = {
    title: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name,
    description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].description,
    keywords: [
        "shopee",
        "thương mại điện tử",
        "mua sắm online",
        "ecommerce"
    ],
    authors: [
        {
            name: "Shopee Clone Team"
        }
    ],
    robots: "index, follow",
    openGraph: {
        title: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].description,
        url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].url,
        siteName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name,
        type: "website"
    },
    twitter: {
        card: "summary_large_image",
        title: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].description
    }
};
const viewport = {
    width: "device-width",
    initialScale: 1
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "vi",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            className: "antialiased",
            children: children
        }, void 0, false, {
            fileName: "[project]/src/app/layout.tsx",
            lineNumber: 37,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
}
}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=_14638926._.js.map