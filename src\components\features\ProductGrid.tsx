import React from 'react'
import ProductCard from './ProductCard'
import { Product } from '@/types'

interface ProductGridProps {
  title: string
  products: Product[]
  className?: string
}

// Mock products data
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Áo thun nam cotton 100% cao cấp, form regular fit, nhiều màu sắc',
    slug: 'ao-thun-nam-cotton-cao-cap',
    description: 'Áo thun nam chất liệu cotton 100% cao cấp',
    price: 199000,
    originalPrice: 299000,
    images: ['/api/placeholder/300/300'],
    category: { id: '1', name: 'Thời trang nam', slug: 'thoi-trang-nam' },
    brand: 'Fashion Brand',
    rating: 4.5,
    reviewCount: 1234,
    sold: 5678,
    stock: 100,
    tags: ['Bán chạy', 'Gi<PERSON> tốt'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON> sneaker nữ thể thao, đế cao su, thiết kế trẻ trung',
    slug: 'giay-sneaker-nu-the-thao',
    description: 'Giày sneaker nữ thể thao cao cấp',
    price: 599000,
    originalPrice: 899000,
    images: ['/api/placeholder/300/300'],
    category: { id: '2', name: 'Giày dép nữ', slug: 'giay-dep-nu' },
    brand: 'Shoe Brand',
    rating: 4.8,
    reviewCount: 567,
    sold: 2345,
    stock: 50,
    tags: ['Hot', 'Freeship'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '3',
    name: 'Túi xách nữ da PU cao cấp, nhiều ngăn tiện dụng',
    slug: 'tui-xach-nu-da-pu-cao-cap',
    description: 'Túi xách nữ da PU cao cấp',
    price: 399000,
    originalPrice: 599000,
    images: ['/api/placeholder/300/300'],
    category: { id: '3', name: 'Túi ví nữ', slug: 'tui-vi-nu' },
    brand: 'Bag Brand',
    rating: 4.3,
    reviewCount: 890,
    sold: 1234,
    stock: 75,
    tags: ['Thời trang'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '4',
    name: 'Đồng hồ nam thể thao chống nước, dây silicon',
    slug: 'dong-ho-nam-the-thao-chong-nuoc',
    description: 'Đồng hồ nam thể thao chống nước',
    price: 799000,
    originalPrice: 1299000,
    images: ['/api/placeholder/300/300'],
    category: { id: '4', name: 'Đồng hồ', slug: 'dong-ho' },
    brand: 'Watch Brand',
    rating: 4.6,
    reviewCount: 456,
    sold: 789,
    stock: 30,
    tags: ['Chống nước', 'Thể thao'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '5',
    name: 'Tai nghe bluetooth 5.0, âm thanh stereo, pin 20h',
    slug: 'tai-nghe-bluetooth-5-0',
    description: 'Tai nghe bluetooth 5.0 cao cấp',
    price: 299000,
    originalPrice: 499000,
    images: ['/api/placeholder/300/300'],
    category: { id: '5', name: 'Thiết bị điện tử', slug: 'thiet-bi-dien-tu' },
    brand: 'Audio Brand',
    rating: 4.4,
    reviewCount: 1567,
    sold: 3456,
    stock: 200,
    tags: ['Bluetooth', 'Pin lâu'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
  {
    id: '6',
    name: 'Ốp lưng iPhone 15 Pro Max trong suốt, chống sốc',
    slug: 'op-lung-iphone-15-pro-max',
    description: 'Ốp lưng iPhone 15 Pro Max chống sốc',
    price: 99000,
    originalPrice: 199000,
    images: ['/api/placeholder/300/300'],
    category: { id: '6', name: 'Phụ kiện điện thoại', slug: 'phu-kien-dien-thoai' },
    brand: 'Case Brand',
    rating: 4.2,
    reviewCount: 2345,
    sold: 6789,
    stock: 500,
    tags: ['Chống sốc', 'Trong suốt'],
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
  },
]

const ProductGrid = ({ title, products = mockProducts, className = '' }: ProductGridProps) => {
  return (
    <div className={`bg-white rounded-lg shadow-shopee ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-border">
        <h2 className="text-xl font-semibold text-text-primary">{title}</h2>
      </div>

      {/* Products Grid */}
      <div className="p-6">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-8">
          <button className="px-8 py-3 border border-primary text-primary hover:bg-primary hover:text-white transition-colors rounded-md font-medium">
            Xem thêm
          </button>
        </div>
      </div>
    </div>
  )
}

export default ProductGrid
