import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Star, MapPin } from 'lucide-react'
import { Product } from '@/types'
import { formatPrice, formatNumber } from '@/lib/utils'

interface ProductCardProps {
  product: Product
  className?: string
}

const ProductCard = ({ product, className = '' }: ProductCardProps) => {
  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  return (
    <Link 
      href={`/product/${product.slug}`}
      className={`block bg-white rounded-lg border border-border hover:shadow-shopee-md transition-shadow group ${className}`}
    >
      {/* Product Image */}
      <div className="aspect-square relative overflow-hidden rounded-t-lg">
        <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
          <span className="text-gray-500 text-sm">Product Image</span>
        </div>
        
        {/* Discount Badge */}
        {discountPercentage > 0 && (
          <div className="absolute top-2 left-2 bg-primary text-white text-xs px-2 py-1 rounded">
            -{discountPercentage}%
          </div>
        )}

        {/* Favorite Button */}
        <button className="absolute top-2 right-2 w-8 h-8 bg-white/80 hover:bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
          <svg className="w-4 h-4 text-text-muted hover:text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>
      </div>

      {/* Product Info */}
      <div className="p-3">
        {/* Product Name */}
        <h3 className="text-sm text-text-primary line-clamp-2 mb-2 group-hover:text-primary transition-colors">
          {product.name}
        </h3>

        {/* Price */}
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-primary font-semibold text-lg">
            {formatPrice(product.price)}
          </span>
          {product.originalPrice && product.originalPrice > product.price && (
            <span className="text-text-muted text-sm line-through">
              {formatPrice(product.originalPrice)}
            </span>
          )}
        </div>

        {/* Rating and Sold */}
        <div className="flex items-center justify-between text-xs text-text-muted mb-2">
          <div className="flex items-center space-x-1">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(product.rating)
                      ? 'text-warning fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span>({formatNumber(product.reviewCount)})</span>
          </div>
          <span>Đã bán {formatNumber(product.sold)}</span>
        </div>

        {/* Location */}
        <div className="flex items-center space-x-1 text-xs text-text-muted">
          <MapPin className="w-3 h-3" />
          <span>TP. Hồ Chí Minh</span>
        </div>

        {/* Tags */}
        {product.tags && product.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {product.tags.slice(0, 2).map((tag) => (
              <span
                key={tag}
                className="text-xs bg-secondary text-text-muted px-2 py-1 rounded"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </Link>
  )
}

export default ProductCard
