{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { Facebook, Instagram, Linkedin } from 'lucide-react'\nimport { FOOTER_LINKS, SOCIAL_LINKS } from '@/constants'\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-white border-t border-border mt-16\">\n      <div className=\"container-shopee\">\n        {/* Main Footer Content */}\n        <div className=\"py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Customer Care */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">CHĂM SÓC KHÁCH HÀNG</h3>\n              <ul className=\"space-y-2\">\n                {FOOTER_LINKS.customerCare.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-sm text-text-secondary hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* About */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">VỀ SHOPEE</h3>\n              <ul className=\"space-y-2\">\n                {FOOTER_LINKS.about.map((link) => (\n                  <li key={link.name}>\n                    <Link \n                      href={link.href}\n                      className=\"text-sm text-text-secondary hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Payment Methods */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">THANH TOÁN</h3>\n              <div className=\"grid grid-cols-3 gap-2\">\n                {FOOTER_LINKS.payment.map((payment) => (\n                  <div \n                    key={payment.name}\n                    className=\"flex items-center justify-center h-8 bg-white border border-border rounded\"\n                  >\n                    <span className=\"text-xs text-text-muted\">{payment.name}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Logistics */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">VẬN CHUYỂN</h3>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {FOOTER_LINKS.logistics.map((logistics) => (\n                  <div \n                    key={logistics.name}\n                    className=\"flex items-center justify-center h-8 bg-white border border-border rounded\"\n                  >\n                    <span className=\"text-xs text-text-muted\">{logistics.name}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Social Media & App Download */}\n        <div className=\"py-8 border-t border-border\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Social Media */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">THEO DÕI CHÚNG TÔI TRÊN</h3>\n              <div className=\"flex space-x-4\">\n                {SOCIAL_LINKS.map((social) => (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"flex items-center space-x-2 text-text-secondary hover:text-primary transition-colors\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    {social.icon === 'facebook' && <Facebook className=\"h-5 w-5\" />}\n                    {social.icon === 'instagram' && <Instagram className=\"h-5 w-5\" />}\n                    {social.icon === 'linkedin' && <Linkedin className=\"h-5 w-5\" />}\n                    <span className=\"text-sm\">{social.name}</span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* App Download */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">TẢI ỨNG DỤNG SHOPEE NGAY THÔI</h3>\n              <div className=\"flex space-x-4\">\n                <div className=\"w-20 h-20 bg-gray-200 rounded border border-border flex items-center justify-center\">\n                  <span className=\"text-xs text-text-muted\">QR Code</span>\n                </div>\n                <div className=\"flex flex-col space-y-2\">\n                  <div className=\"h-8 bg-gray-200 rounded border border-border flex items-center justify-center px-4\">\n                    <span className=\"text-xs text-text-muted\">App Store</span>\n                  </div>\n                  <div className=\"h-8 bg-gray-200 rounded border border-border flex items-center justify-center px-4\">\n                    <span className=\"text-xs text-text-muted\">Google Play</span>\n                  </div>\n                  <div className=\"h-8 bg-gray-200 rounded border border-border flex items-center justify-center px-4\">\n                    <span className=\"text-xs text-text-muted\">AppGallery</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Certifications */}\n            <div>\n              <h3 className=\"font-semibold text-text-primary mb-4\">BẢO ĐẢM</h3>\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div className=\"h-12 bg-gray-200 rounded border border-border flex items-center justify-center\">\n                  <span className=\"text-xs text-text-muted\">Chính hãng</span>\n                </div>\n                <div className=\"h-12 bg-gray-200 rounded border border-border flex items-center justify-center\">\n                  <span className=\"text-xs text-text-muted\">Bảo mật</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <div className=\"py-6 border-t border-border\">\n          <div className=\"text-center\">\n            <div className=\"text-sm text-text-muted mb-2\">\n              © 2024 Shopee Clone. Tất cả các quyền được bảo lưu.\n            </div>\n            <div className=\"text-xs text-text-muted\">\n              Quốc gia & Khu vực: Singapore | Indonesia | Thái Lan | Malaysia | Việt Nam | Philippines | Brazil | México | Colombia | Chile | Đài Loan\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAAA;AAAA;AACA;;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAG,WAAU;kDACX,yHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC9B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAG,WAAU;kDACX,yHAAA,CAAA,eAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,yHAAA,CAAA,eAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,wBACzB,8OAAC;gDAEC,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DAA2B,QAAQ,IAAI;;;;;;+CAHlD,QAAQ,IAAI;;;;;;;;;;;;;;;;0CAUzB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,yHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,0BAC3B,8OAAC;gDAEC,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DAA2B,UAAU,IAAI;;;;;;+CAHpD,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAY/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,yHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,uBACjB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,OAAO,IAAI;gDACjB,WAAU;gDACV,QAAO;gDACP,KAAI;;oDAEH,OAAO,IAAI,KAAK,4BAAc,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAClD,OAAO,IAAI,KAAK,6BAAe,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpD,OAAO,IAAI,KAAK,4BAAc,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACnD,8OAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;;+CATjC,OAAO,IAAI;;;;;;;;;;;;;;;;0CAgBxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;0DAE5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOlD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;0DAE5C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;0CAG9C,8OAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react'\nimport Header from './Header'\nimport Navigation from './Navigation'\nimport Footer from './Footer'\n\ninterface LayoutProps {\n  children: React.ReactNode\n  showNavigation?: boolean\n}\n\nconst Layout = ({ children, showNavigation = true }: LayoutProps) => {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      {showNavigation && <Navigation />}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  )\n}\n\nexport default Layout\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAOA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,iBAAiB,IAAI,EAAe;IAC9D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;YACN,gCAAkB,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAC9B,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/app/page.tsx"], "sourcesContent": ["import Layout from '@/components/layout/Layout'\n\nexport default function Home() {\n  return (\n    <Layout>\n      <div className=\"container-shopee py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-primary mb-4\">\n            Chào mừng đến với Shop<PERSON>\n          </h1>\n          <p className=\"text-lg text-text-secondary mb-8\">\n            Nền tảng thương mại điện tử hàng đầu Việt Nam\n          </p>\n          <div className=\"bg-white rounded-lg shadow-shopee-md p-8\">\n            <h2 className=\"text-2xl font-semibold text-text-primary mb-4\">\n              Dự án đang được phát triển\n            </h2>\n            <p className=\"text-text-secondary\">\n              Chúng tôi đang xây dựng một clone hoàn chỉnh của Shopee với tất cả các tính năng chính.\n              H<PERSON>y quay lại sau để trải nghiệm!\n            </p>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC,sIAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}]}