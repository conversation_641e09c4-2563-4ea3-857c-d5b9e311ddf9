This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.


Tuyệt vời! Tôi đã hoàn thành việc phát triển trang chủ với đầy đủ các component chính của Shopee:

1. Setup dự án và cấu hình môi trường
✅ Khởi tạo Next.js 14 với TypeScript
✅ Cấu hình Tailwind CSS với theme Shopee
✅ Setup Zustand cho state management
✅ Cài đặt các dependencies cần thiết (Framer Motion, React Hook Form, Zod, Lucide React, etc.)
2. Tạo layout và component cơ bản
✅ Header component với search bar, cart icon, user menu
✅ Navigation component với dropdown categories
✅ Footer component với đầy đủ links và thông tin
✅ Layout component tổng hợp
✅ UI components: Button, Input với variants và styling
3. Phát triển trang chủ (Homepage)
✅ BannerSlider với auto-play và navigation
✅ CategoriesGrid hiển thị 20 danh mục chính
✅ FlashSale với countdown timer và product carousel
✅ ProductCard với rating, discount, location
✅ ProductGrid cho sản phẩm gợi ý
✅ Responsive design hoàn chỉnh
4. Kiến trúc và Clean Code
✅ Folder structure rõ ràng và có tổ chức
✅ TypeScript types đầy đủ cho tất cả entities
✅ Constants tập trung cho colors, categories, links
✅ Utility functions cho formatting và styling
✅ Store management với Zustand
✅ Component reusability cao
5. Shopee-like Features
✅ Màu sắc và theme giống y hệt Shopee (#ee4d2d primary color)
✅ Layout structure tương tự Shopee
✅ Component behavior như slider, countdown, hover effects
✅ Typography và spacing phù hợp
✅ Icons và visual elements tương đồng
🚀 Kết quả hiện tại:

Trang web hiện tại đã có:

Header với logo Shopee, search bar, cart icon, user menu
Navigation với dropdown categories
Banner slider với auto-play
Categories grid với 20 danh mục chính
Flash Sale section với countdown timer
Product grids cho sản phẩm gợi ý
Footer đầy đủ thông tin
Responsive design hoạt động trên mọi device

📋 Các task tiếp theo:
Bạn có muốn tôi tiếp tục với các task còn lại không?

Tạo trang danh mục sản phẩm - với filter, sort, pagination
Phát triển trang chi tiết sản phẩm - với image gallery, reviews
Tạo trang giỏ hàng và checkout - shopping cart flow
Phát triển tính năng tìm kiếm - search với autocomplete
Tối ưu hóa responsive và performance
Testing và documentation