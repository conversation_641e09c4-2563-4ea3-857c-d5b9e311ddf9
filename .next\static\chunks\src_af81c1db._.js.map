{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('vi-VN', {\n    style: 'currency',\n    currency: 'VND',\n  }).format(price)\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('vi-VN').format(num)\n}\n\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[đĐ]/g, 'd')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .trim()\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,iBAAiB,IACzB,IAAI,GACJ,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-white shadow hover:bg-primary-hover',\n        destructive: 'bg-error text-white shadow-sm hover:bg-error/90',\n        outline: 'border border-border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n        success: 'bg-success text-white shadow hover:bg-success/90',\n        warning: 'bg-warning text-white shadow hover:bg-warning/90',\n      },\n      size: {\n        default: 'h-9 px-4 py-2',\n        sm: 'h-8 rounded-md px-3 text-xs',\n        lg: 'h-10 rounded-md px-8',\n        xl: 'h-12 rounded-md px-10 text-base',\n        icon: 'h-9 w-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAA+F;QAA9F,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC3F,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: string\n  label?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, label, leftIcon, rightIcon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"mb-2 block text-sm font-medium text-text-primary\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 -translate-y-1/2 text-text-muted\">\n              {leftIcon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'flex h-9 w-full rounded-md border border-border bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-error focus-visible:ring-error',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-text-muted\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-error\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAAmE;QAAlE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;IAC/D,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sUACA,YAAY,SACZ,aAAa,SACb,SAAS,yCACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,uBACC,6LAAC;gBAAE,WAAU;0BAA2B;;;;;;;;;;;;AAIhD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/constants/index.ts"], "sourcesContent": ["// App Configuration\nexport const APP_CONFIG = {\n  name: '<PERSON><PERSON> Clone',\n  description: '<PERSON><PERSON> của trang thương mại điện tử Shopee',\n  url: 'http://localhost:3000',\n  version: '1.0.0',\n} as const\n\n// API Configuration\nexport const API_CONFIG = {\n  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',\n  timeout: 10000,\n} as const\n\n// Shopee Brand Colors\nexport const COLORS = {\n  primary: '#ee4d2d',\n  primaryHover: '#d73211',\n  secondary: '#f5f5f5',\n  accent: '#26aa99',\n  warning: '#ffbf00',\n  error: '#ff424f',\n  success: '#00c851',\n  text: {\n    primary: '#222222',\n    secondary: '#757575',\n    muted: '#999999',\n  },\n  border: '#e5e5e5',\n  background: '#f5f5f5',\n} as const\n\n// Navigation Menu Items\nexport const MAIN_CATEGORIES = [\n  { id: '1', name: '<PERSON>h<PERSON><PERSON> T<PERSON>', slug: 'thoi-trang-nam', icon: '👔' },\n  { id: '2', name: '<PERSON>h<PERSON><PERSON>', slug: 'thoi-trang-nu', icon: '👗' },\n  { id: '3', name: 'Điện Thoại & Phụ Kiện', slug: 'dien-thoai-phu-kien', icon: '📱' },\n  { id: '4', name: 'Máy Tính & Laptop', slug: 'may-tinh-laptop', icon: '💻' },\n  { id: '5', name: 'Máy Ảnh & Máy Quay Phim', slug: 'may-anh-may-quay', icon: '📷' },\n  { id: '6', name: 'Đồng Hồ', slug: 'dong-ho', icon: '⌚' },\n  { id: '7', name: 'Giày Dép Nam', slug: 'giay-dep-nam', icon: '👞' },\n  { id: '8', name: 'Giày Dép Nữ', slug: 'giay-dep-nu', icon: '👠' },\n  { id: '9', name: 'Túi Ví Nam', slug: 'tui-vi-nam', icon: '👜' },\n  { id: '10', name: 'Túi Ví Nữ', slug: 'tui-vi-nu', icon: '👛' },\n  { id: '11', name: 'Thiết Bị Điện Tử', slug: 'thiet-bi-dien-tu', icon: '🔌' },\n  { id: '12', name: 'Ô Tô & Xe Máy & Xe Đạp', slug: 'o-to-xe-may-xe-dap', icon: '🚗' },\n  { id: '13', name: 'Nhà Cửa & Đời Sống', slug: 'nha-cua-doi-song', icon: '🏠' },\n  { id: '14', name: 'Sắc Đẹp', slug: 'sac-dep', icon: '💄' },\n  { id: '15', name: 'Sức Khỏe', slug: 'suc-khoe', icon: '💊' },\n  { id: '16', name: 'Thể Thao & Du Lịch', slug: 'the-thao-du-lich', icon: '⚽' },\n  { id: '17', name: 'Bách Hóa Online', slug: 'bach-hoa-online', icon: '🛒' },\n  { id: '18', name: 'Nhà Sách Online', slug: 'nha-sach-online', icon: '📚' },\n] as const\n\n// Quick Links\nexport const QUICK_LINKS = [\n  { name: 'Kênh Người Bán', href: '/seller' },\n  { name: 'Trở thành Người bán Shopee', href: '/seller/register' },\n  { name: 'Tải ứng dụng', href: '/download' },\n  { name: 'Kết nối', href: '/connect' },\n] as const\n\n// Footer Links\nexport const FOOTER_LINKS = {\n  customerCare: [\n    { name: 'Trung Tâm Trợ Giúp', href: '/help' },\n    { name: 'Shopee Blog', href: '/blog' },\n    { name: 'Shopee Mall', href: '/mall' },\n    { name: 'Hướng Dẫn Mua Hàng', href: '/guide/buy' },\n    { name: 'Hướng Dẫn Bán Hàng', href: '/guide/sell' },\n    { name: 'Thanh Toán', href: '/payment' },\n    { name: 'Shopee Xu', href: '/coins' },\n    { name: 'Vận Chuyển', href: '/shipping' },\n    { name: 'Trả Hàng & Hoàn Tiền', href: '/returns' },\n    { name: 'Chăm Sóc Khách Hàng', href: '/support' },\n    { name: 'Chính Sách Bảo Hành', href: '/warranty' },\n  ],\n  about: [\n    { name: 'Giới Thiệu Về Shopee Việt Nam', href: '/about' },\n    { name: 'Tuyển Dụng', href: '/careers' },\n    { name: 'Điều Khoản Shopee', href: '/terms' },\n    { name: 'Chính Sách Bảo Mật', href: '/privacy' },\n    { name: 'Chính Hãng', href: '/authentic' },\n    { name: 'Kênh Người Bán', href: '/seller' },\n    { name: 'Flash Sales', href: '/flash-sales' },\n    { name: 'Chương Trình Tiếp Thị Liên Kết Shopee', href: '/affiliate' },\n    { name: 'Liên Hệ Với Truyền Thông', href: '/media' },\n  ],\n  payment: [\n    { name: 'Visa', icon: '/icons/visa.svg' },\n    { name: 'Mastercard', icon: '/icons/mastercard.svg' },\n    { name: 'JCB', icon: '/icons/jcb.svg' },\n    { name: 'American Express', icon: '/icons/amex.svg' },\n    { name: 'COD', icon: '/icons/cod.svg' },\n    { name: 'Shopee Pay', icon: '/icons/shopeepay.svg' },\n    { name: 'SPayLater', icon: '/icons/spaylater.svg' },\n  ],\n  logistics: [\n    { name: 'Shopee Express', icon: '/icons/shopee-express.svg' },\n    { name: 'Giao Hàng Tiết Kiệm', icon: '/icons/ghtk.svg' },\n    { name: 'GHN', icon: '/icons/ghn.svg' },\n    { name: 'Viettel Post', icon: '/icons/viettel-post.svg' },\n    { name: 'Vietnam Post', icon: '/icons/vietnam-post.svg' },\n    { name: 'J&T Express', icon: '/icons/jt-express.svg' },\n    { name: 'Grab Express', icon: '/icons/grab-express.svg' },\n    { name: 'Ninja Van', icon: '/icons/ninja-van.svg' },\n    { name: 'Best Express', icon: '/icons/best-express.svg' },\n    { name: 'Be', icon: '/icons/be.svg' },\n  ],\n} as const\n\n// Social Media Links\nexport const SOCIAL_LINKS = [\n  { name: 'Facebook', href: 'https://facebook.com/ShopeeVN', icon: 'facebook' },\n  { name: 'Instagram', href: 'https://instagram.com/shopee_vn', icon: 'instagram' },\n  { name: 'LinkedIn', href: 'https://linkedin.com/company/shopee', icon: 'linkedin' },\n] as const\n\n// Pagination\nexport const PAGINATION = {\n  defaultLimit: 20,\n  maxLimit: 100,\n} as const\n\n// Image Sizes\nexport const IMAGE_SIZES = {\n  thumbnail: { width: 100, height: 100 },\n  small: { width: 200, height: 200 },\n  medium: { width: 400, height: 400 },\n  large: { width: 800, height: 800 },\n  banner: { width: 1200, height: 400 },\n} as const\n\n// Breakpoints (matching Tailwind CSS)\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  cart: 'shopee_cart',\n  user: 'shopee_user',\n  wishlist: 'shopee_wishlist',\n  recentlyViewed: 'shopee_recently_viewed',\n  searchHistory: 'shopee_search_history',\n} as const\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  network: 'Lỗi kết nối mạng. Vui lòng thử lại.',\n  server: 'Lỗi máy chủ. Vui lòng thử lại sau.',\n  notFound: 'Không tìm thấy trang.',\n  unauthorized: 'Bạn cần đăng nhập để thực hiện hành động này.',\n  forbidden: 'Bạn không có quyền truy cập.',\n  validation: 'Dữ liệu không hợp lệ.',\n  unknown: 'Đã xảy ra lỗi không xác định.',\n} as const\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;;;;;;AAUT;AATJ,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;AACX;AAGO,MAAM,aAAa;IACxB,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;IAC5C,SAAS;AACX;AAGO,MAAM,SAAS;IACpB,SAAS;IACT,cAAc;IACd,WAAW;IACX,QAAQ;IACR,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;QACJ,SAAS;QACT,WAAW;QACX,OAAO;IACT;IACA,QAAQ;IACR,YAAY;AACd;AAGO,MAAM,kBAAkB;IAC7B;QAAE,IAAI;QAAK,MAAM;QAAkB,MAAM;QAAkB,MAAM;IAAK;IACtE;QAAE,IAAI;QAAK,MAAM;QAAiB,MAAM;QAAiB,MAAM;IAAK;IACpE;QAAE,IAAI;QAAK,MAAM;QAAyB,MAAM;QAAuB,MAAM;IAAK;IAClF;QAAE,IAAI;QAAK,MAAM;QAAqB,MAAM;QAAmB,MAAM;IAAK;IAC1E;QAAE,IAAI;QAAK,MAAM;QAA2B,MAAM;QAAoB,MAAM;IAAK;IACjF;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;QAAW,MAAM;IAAI;IACvD;QAAE,IAAI;QAAK,MAAM;QAAgB,MAAM;QAAgB,MAAM;IAAK;IAClE;QAAE,IAAI;QAAK,MAAM;QAAe,MAAM;QAAe,MAAM;IAAK;IAChE;QAAE,IAAI;QAAK,MAAM;QAAc,MAAM;QAAc,MAAM;IAAK;IAC9D;QAAE,IAAI;QAAM,MAAM;QAAa,MAAM;QAAa,MAAM;IAAK;IAC7D;QAAE,IAAI;QAAM,MAAM;QAAoB,MAAM;QAAoB,MAAM;IAAK;IAC3E;QAAE,IAAI;QAAM,MAAM;QAA0B,MAAM;QAAsB,MAAM;IAAK;IACnF;QAAE,IAAI;QAAM,MAAM;QAAsB,MAAM;QAAoB,MAAM;IAAK;IAC7E;QAAE,IAAI;QAAM,MAAM;QAAW,MAAM;QAAW,MAAM;IAAK;IACzD;QAAE,IAAI;QAAM,MAAM;QAAY,MAAM;QAAY,MAAM;IAAK;IAC3D;QAAE,IAAI;QAAM,MAAM;QAAsB,MAAM;QAAoB,MAAM;IAAI;IAC5E;QAAE,IAAI;QAAM,MAAM;QAAmB,MAAM;QAAmB,MAAM;IAAK;IACzE;QAAE,IAAI;QAAM,MAAM;QAAmB,MAAM;QAAmB,MAAM;IAAK;CAC1E;AAGM,MAAM,cAAc;IACzB;QAAE,MAAM;QAAkB,MAAM;IAAU;IAC1C;QAAE,MAAM;QAA8B,MAAM;IAAmB;IAC/D;QAAE,MAAM;QAAgB,MAAM;IAAY;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAGM,MAAM,eAAe;IAC1B,cAAc;QACZ;YAAE,MAAM;YAAsB,MAAM;QAAQ;QAC5C;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAsB,MAAM;QAAc;QAClD;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAc,MAAM;QAAY;QACxC;YAAE,MAAM;YAAwB,MAAM;QAAW;QACjD;YAAE,MAAM;YAAuB,MAAM;QAAW;QAChD;YAAE,MAAM;YAAuB,MAAM;QAAY;KAClD;IACD,OAAO;QACL;YAAE,MAAM;YAAiC,MAAM;QAAS;QACxD;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAqB,MAAM;QAAS;QAC5C;YAAE,MAAM;YAAsB,MAAM;QAAW;QAC/C;YAAE,MAAM;YAAc,MAAM;QAAa;QACzC;YAAE,MAAM;YAAkB,MAAM;QAAU;QAC1C;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAyC,MAAM;QAAa;QACpE;YAAE,MAAM;YAA4B,MAAM;QAAS;KACpD;IACD,SAAS;QACP;YAAE,MAAM;YAAQ,MAAM;QAAkB;QACxC;YAAE,MAAM;YAAc,MAAM;QAAwB;QACpD;YAAE,MAAM;YAAO,MAAM;QAAiB;QACtC;YAAE,MAAM;YAAoB,MAAM;QAAkB;QACpD;YAAE,MAAM;YAAO,MAAM;QAAiB;QACtC;YAAE,MAAM;YAAc,MAAM;QAAuB;QACnD;YAAE,MAAM;YAAa,MAAM;QAAuB;KACnD;IACD,WAAW;QACT;YAAE,MAAM;YAAkB,MAAM;QAA4B;QAC5D;YAAE,MAAM;YAAuB,MAAM;QAAkB;QACvD;YAAE,MAAM;YAAO,MAAM;QAAiB;QACtC;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAe,MAAM;QAAwB;QACrD;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAa,MAAM;QAAuB;QAClD;YAAE,MAAM;YAAgB,MAAM;QAA0B;QACxD;YAAE,MAAM;YAAM,MAAM;QAAgB;KACrC;AACH;AAGO,MAAM,eAAe;IAC1B;QAAE,MAAM;QAAY,MAAM;QAAiC,MAAM;IAAW;IAC5E;QAAE,MAAM;QAAa,MAAM;QAAmC,MAAM;IAAY;IAChF;QAAE,MAAM;QAAY,MAAM;QAAuC,MAAM;IAAW;CACnF;AAGM,MAAM,aAAa;IACxB,cAAc;IACd,UAAU;AACZ;AAGO,MAAM,cAAc;IACzB,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,OAAO;QAAE,OAAO;QAAK,QAAQ;IAAI;IACjC,QAAQ;QAAE,OAAO;QAAK,QAAQ;IAAI;IAClC,OAAO;QAAE,OAAO;QAAK,QAAQ;IAAI;IACjC,QAAQ;QAAE,OAAO;QAAM,QAAQ;IAAI;AACrC;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,eAAe;IAC1B,MAAM;IACN,MAAM;IACN,UAAU;IACV,gBAAgB;IAChB,eAAe;AACjB;AAGO,MAAM,iBAAiB;IAC5B,SAAS;IACT,QAAQ;IACR,UAAU;IACV,cAAc;IACd,WAAW;IACX,YAAY;IACZ,SAAS;AACX", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/store/cart.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { CartItem, Product, ProductVariant } from '@/types'\nimport { STORAGE_KEYS } from '@/constants'\n\ninterface CartState {\n  items: CartItem[]\n  isOpen: boolean\n  addItem: (product: Product, variant?: ProductVariant, quantity?: number) => void\n  removeItem: (itemId: string) => void\n  updateQuantity: (itemId: string, quantity: number) => void\n  toggleItemSelection: (itemId: string) => void\n  selectAllItems: (selected: boolean) => void\n  clearCart: () => void\n  openCart: () => void\n  closeCart: () => void\n  getTotalItems: () => number\n  getTotalPrice: () => number\n  getSelectedItems: () => CartItem[]\n  getSelectedTotalPrice: () => number\n}\n\nexport const useCartStore = create<CartState>()(\n  persist(\n    (set, get) => ({\n      items: [],\n      isOpen: false,\n\n      addItem: (product, variant, quantity = 1) => {\n        set((state) => {\n          const existingItemIndex = state.items.findIndex(\n            (item) =>\n              item.product.id === product.id &&\n              item.variant?.id === variant?.id\n          )\n\n          if (existingItemIndex > -1) {\n            // Update existing item quantity\n            const updatedItems = [...state.items]\n            updatedItems[existingItemIndex].quantity += quantity\n            return { items: updatedItems }\n          } else {\n            // Add new item\n            const newItem: CartItem = {\n              id: `${product.id}-${variant?.id || 'default'}-${Date.now()}`,\n              product,\n              variant,\n              quantity,\n              selected: true,\n            }\n            return { items: [...state.items, newItem] }\n          }\n        })\n      },\n\n      removeItem: (itemId) => {\n        set((state) => ({\n          items: state.items.filter((item) => item.id !== itemId),\n        }))\n      },\n\n      updateQuantity: (itemId, quantity) => {\n        if (quantity <= 0) {\n          get().removeItem(itemId)\n          return\n        }\n\n        set((state) => ({\n          items: state.items.map((item) =>\n            item.id === itemId ? { ...item, quantity } : item\n          ),\n        }))\n      },\n\n      toggleItemSelection: (itemId) => {\n        set((state) => ({\n          items: state.items.map((item) =>\n            item.id === itemId ? { ...item, selected: !item.selected } : item\n          ),\n        }))\n      },\n\n      selectAllItems: (selected) => {\n        set((state) => ({\n          items: state.items.map((item) => ({ ...item, selected })),\n        }))\n      },\n\n      clearCart: () => {\n        set({ items: [] })\n      },\n\n      openCart: () => {\n        set({ isOpen: true })\n      },\n\n      closeCart: () => {\n        set({ isOpen: false })\n      },\n\n      getTotalItems: () => {\n        return get().items.reduce((total, item) => total + item.quantity, 0)\n      },\n\n      getTotalPrice: () => {\n        return get().items.reduce((total, item) => {\n          const price = item.variant?.price || item.product.price\n          return total + price * item.quantity\n        }, 0)\n      },\n\n      getSelectedItems: () => {\n        return get().items.filter((item) => item.selected)\n      },\n\n      getSelectedTotalPrice: () => {\n        return get()\n          .getSelectedItems()\n          .reduce((total, item) => {\n            const price = item.variant?.price || item.product.price\n            return total + price * item.quantity\n          }, 0)\n      },\n    }),\n    {\n      name: STORAGE_KEYS.cart,\n      partialize: (state) => ({ items: state.items }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAmBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,QAAQ;QAER,SAAS,SAAC,SAAS;gBAAS,4EAAW;YACrC,IAAI,CAAC;gBACH,MAAM,oBAAoB,MAAM,KAAK,CAAC,SAAS,CAC7C,CAAC;wBAEC;2BADA,KAAK,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE,IAC9B,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,EAAE,OAAK,oBAAA,8BAAA,QAAS,EAAE;;gBAGpC,IAAI,oBAAoB,CAAC,GAAG;oBAC1B,gCAAgC;oBAChC,MAAM,eAAe;2BAAI,MAAM,KAAK;qBAAC;oBACrC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,IAAI;oBAC5C,OAAO;wBAAE,OAAO;oBAAa;gBAC/B,OAAO;oBACL,eAAe;oBACf,MAAM,UAAoB;wBACxB,IAAI,AAAC,GAAgB,OAAd,QAAQ,EAAE,EAAC,KAA+B,OAA5B,CAAA,oBAAA,8BAAA,QAAS,EAAE,KAAI,WAAU,KAAc,OAAX,KAAK,GAAG;wBACzD;wBACA;wBACA;wBACA,UAAU;oBACZ;oBACA,OAAO;wBAAE,OAAO;+BAAI,MAAM,KAAK;4BAAE;yBAAQ;oBAAC;gBAC5C;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAClD,CAAC;QACH;QAEA,gBAAgB,CAAC,QAAQ;YACvB,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC;gBACjB;YACF;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OACtB,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;wBAAS,IAAI;gBAEjD,CAAC;QACH;QAEA,qBAAqB,CAAC;YACpB,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OACtB,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE,UAAU,CAAC,KAAK,QAAQ;wBAAC,IAAI;gBAEjE,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAS,CAAC;gBACzD,CAAC;QACH;QAEA,WAAW;YACT,IAAI;gBAAE,OAAO,EAAE;YAAC;QAClB;QAEA,UAAU;YACR,IAAI;gBAAE,QAAQ;YAAK;QACrB;QAEA,WAAW;YACT,IAAI;gBAAE,QAAQ;YAAM;QACtB;QAEA,eAAe;YACb,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;QACpE;QAEA,eAAe;YACb,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO;oBAClB;gBAAd,MAAM,QAAQ,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,KAAK,KAAI,KAAK,OAAO,CAAC,KAAK;gBACvD,OAAO,QAAQ,QAAQ,KAAK,QAAQ;YACtC,GAAG;QACL;QAEA,kBAAkB;YAChB,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;QACnD;QAEA,uBAAuB;YACrB,OAAO,MACJ,gBAAgB,GAChB,MAAM,CAAC,CAAC,OAAO;oBACA;gBAAd,MAAM,QAAQ,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,KAAK,KAAI,KAAK,OAAO,CAAC,KAAK;gBACvD,OAAO,QAAQ,QAAQ,KAAK,QAAQ;YACtC,GAAG;QACP;IACF,CAAC,GACD;IACE,MAAM,4HAAA,CAAA,eAAY,CAAC,IAAI;IACvB,YAAY,CAAC,QAAU,CAAC;YAAE,OAAO,MAAM,KAAK;QAAC,CAAC;AAChD", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/store/auth.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { User } from '@/types'\nimport { STORAGE_KEYS } from '@/constants'\n\ninterface AuthState {\n  user: User | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  login: (user: User) => void\n  logout: () => void\n  updateUser: (userData: Partial<User>) => void\n  setLoading: (loading: boolean) => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n\n      login: (user) => {\n        set({\n          user,\n          isAuthenticated: true,\n          isLoading: false,\n        })\n      },\n\n      logout: () => {\n        set({\n          user: null,\n          isAuthenticated: false,\n          isLoading: false,\n        })\n      },\n\n      updateUser: (userData) => {\n        const currentUser = get().user\n        if (currentUser) {\n          set({\n            user: { ...currentUser, ...userData },\n          })\n        }\n      },\n\n      setLoading: (loading) => {\n        set({ isLoading: loading })\n      },\n    }),\n    {\n      name: STORAGE_KEYS.user,\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAYO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,CAAC;YACN,IAAI;gBACF;gBACA,iBAAiB;gBACjB,WAAW;YACb;QACF;QAEA,QAAQ;YACN,IAAI;gBACF,MAAM;gBACN,iBAAiB;gBACjB,WAAW;YACb;QACF;QAEA,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM;wBAAE,GAAG,WAAW;wBAAE,GAAG,QAAQ;oBAAC;gBACtC;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;IACF,CAAC,GACD;IACE,MAAM,4HAAA,CAAA,eAAY,CAAC,IAAI;IACvB,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { Search, ShoppingCart, Bell, User, Globe, HelpCircle } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { useCartStore } from '@/store/cart'\nimport { useAuthStore } from '@/store/auth'\nimport { QUICK_LINKS } from '@/constants'\nimport { cn } from '@/lib/utils'\n\nconst Header = () => {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { getTotalItems } = useCartStore()\n  const { isAuthenticated, user } = useAuthStore()\n  const totalItems = getTotalItems()\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      // Navigate to search results\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`\n    }\n  }\n\n  return (\n    <header className=\"bg-white shadow-shopee\">\n      {/* Top Bar */}\n      <div className=\"bg-primary\">\n        <div className=\"container-shopee\">\n          <div className=\"flex h-8 items-center justify-between text-xs text-white\">\n            {/* Left Links */}\n            <div className=\"flex items-center space-x-4\">\n              {QUICK_LINKS.map((link) => (\n                <Link\n                  key={link.name}\n                  href={link.href}\n                  className=\"hover:text-gray-200 transition-colors\"\n                >\n                  {link.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* Right Links */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-1\">\n                <Bell className=\"h-3 w-3\" />\n                <span>Thông Báo</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <HelpCircle className=\"h-3 w-3\" />\n                <span>Hỗ Trợ</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <Globe className=\"h-3 w-3\" />\n                <span>Tiếng Việt</span>\n              </div>\n              {isAuthenticated ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-4 w-4 rounded-full bg-gray-300\"></div>\n                  <span>{user?.name}</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <Link href=\"/register\" className=\"hover:text-gray-200\">\n                    Đăng Ký\n                  </Link>\n                  <span>|</span>\n                  <Link href=\"/login\" className=\"hover:text-gray-200\">\n                    Đăng Nhập\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div className=\"container-shopee\">\n        <div className=\"flex h-20 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-primary\">\n              Shopee\n            </div>\n          </Link>\n\n          {/* Search Bar */}\n          <div className=\"flex-1 max-w-2xl mx-8\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <Input\n                type=\"text\"\n                placeholder=\"Tìm kiếm sản phẩm, thương hiệu và tên shop\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"h-10 pr-12 border-primary focus-visible:ring-primary\"\n                rightIcon={\n                  <Button\n                    type=\"submit\"\n                    size=\"sm\"\n                    className=\"absolute right-0 top-0 h-full rounded-l-none\"\n                  >\n                    <Search className=\"h-4 w-4\" />\n                  </Button>\n                }\n              />\n            </form>\n\n            {/* Popular Searches */}\n            <div className=\"mt-2 flex flex-wrap gap-2\">\n              {[\n                'Áo thun',\n                'Điện thoại',\n                'Giày sneaker',\n                'Túi xách',\n                'Đồng hồ',\n                'Laptop',\n              ].map((keyword) => (\n                <Link\n                  key={keyword}\n                  href={`/search?q=${encodeURIComponent(keyword)}`}\n                  className=\"text-xs text-text-muted hover:text-primary transition-colors\"\n                >\n                  {keyword}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Right Actions */}\n          <div className=\"flex items-center space-x-6\">\n            {/* Cart */}\n            <Link href=\"/cart\" className=\"relative\">\n              <ShoppingCart className=\"h-6 w-6 text-text-primary hover:text-primary transition-colors\" />\n              {totalItems > 0 && (\n                <span className=\"absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-white\">\n                  {totalItems > 99 ? '99+' : totalItems}\n                </span>\n              )}\n            </Link>\n\n            {/* User Menu */}\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300\"></div>\n                <span className=\"text-sm text-text-primary\">{user?.name}</span>\n              </div>\n            ) : (\n              <Link href=\"/login\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <User className=\"mr-2 h-4 w-4\" />\n                  Đăng nhập\n                </Button>\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAaA,MAAM,SAAS;;IACb,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,aAAa;IAEnB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,6BAA6B;YAC7B,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,aAAmD,OAAvC,mBAAmB,YAAY,IAAI;QACzE;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,4HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,qBAChB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;0CAUpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iOAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;oCAEP,gCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAM,iBAAA,2BAAA,KAAM,IAAI;;;;;;;;;;;iGAGnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAsB;;;;;;0DAGvD,6LAAC;0DAAK;;;;;;0DACN,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAMnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,UAAU;oCAAc,WAAU;8CACtC,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,yBACE,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;8CAO1B,6LAAC;oCAAI,WAAU;8CACZ;wCACC;wCACA;wCACA;wCACA;wCACA;wCACA;qCACD,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,AAAC,aAAwC,OAA5B,mBAAmB;4CACtC,WAAU;sDAET;2CAJI;;;;;;;;;;;;;;;;sCAWb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,aAAa,mBACZ,6LAAC;4CAAK,WAAU;sDACb,aAAa,KAAK,QAAQ;;;;;;;;;;;;gCAMhC,gCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA6B,iBAAA,2BAAA,KAAM,IAAI;;;;;;;;;;;6FAGzD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GAvJM;;QAEsB,uHAAA,CAAA,eAAY;QACJ,uHAAA,CAAA,eAAY;;;KAH1C;uCAyJS", "debugId": null}}, {"offset": {"line": 1267, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/gitproject/shope_clone_url/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { ChevronRight, Menu } from 'lucide-react'\nimport { MAIN_CATEGORIES } from '@/constants'\nimport { cn } from '@/lib/utils'\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null)\n\n  return (\n    <nav className=\"bg-white border-b border-border\">\n      <div className=\"container-shopee\">\n        <div className=\"flex items-center h-12\">\n          {/* Categories Dropdown */}\n          <div \n            className=\"relative\"\n            onMouseEnter={() => setIsOpen(true)}\n            onMouseLeave={() => setIsOpen(false)}\n          >\n            <button className=\"flex items-center space-x-2 px-4 py-2 text-text-primary hover:text-primary transition-colors\">\n              <Menu className=\"h-4 w-4\" />\n              <span className=\"font-medium\"><PERSON>h <PERSON></span>\n            </button>\n\n            {/* Dropdown Menu */}\n            {isOpen && (\n              <div className=\"absolute top-full left-0 z-50 w-64 bg-white border border-border shadow-shopee-lg rounded-md overflow-hidden\">\n                <div className=\"py-2\">\n                  {MAIN_CATEGORIES.map((category) => (\n                    <Link\n                      key={category.id}\n                      href={`/category/${category.slug}`}\n                      className={cn(\n                        \"flex items-center justify-between px-4 py-3 text-sm text-text-primary hover:bg-secondary hover:text-primary transition-colors\",\n                        hoveredCategory === category.id && \"bg-secondary text-primary\"\n                      )}\n                      onMouseEnter={() => setHoveredCategory(category.id)}\n                      onMouseLeave={() => setHoveredCategory(null)}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-lg\">{category.icon}</span>\n                        <span>{category.name}</span>\n                      </div>\n                      <ChevronRight className=\"h-4 w-4\" />\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Quick Navigation Links */}\n          <div className=\"flex items-center space-x-8 ml-8\">\n            <Link \n              href=\"/flash-sale\" \n              className=\"text-sm font-medium text-primary hover:text-primary-hover transition-colors\"\n            >\n              Flash Sale\n            </Link>\n            <Link \n              href=\"/mall\" \n              className=\"text-sm font-medium text-text-primary hover:text-primary transition-colors\"\n            >\n              Shopee Mall\n            </Link>\n            <Link \n              href=\"/free-shipping\" \n              className=\"text-sm font-medium text-text-primary hover:text-primary transition-colors\"\n            >\n              Miễn Phí Ship\n            </Link>\n            <Link \n              href=\"/vouchers\" \n              className=\"text-sm font-medium text-text-primary hover:text-primary transition-colors\"\n            >\n              Voucher\n            </Link>\n            <Link \n              href=\"/live\" \n              className=\"text-sm font-medium text-text-primary hover:text-primary transition-colors\"\n            >\n              Shopee Live\n            </Link>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,cAAc,IAAM,UAAU;wBAC9B,cAAc,IAAM,UAAU;;0CAE9B,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;4BAI/B,wBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,4HAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,yBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,AAAC,aAA0B,OAAd,SAAS,IAAI;4CAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iIACA,oBAAoB,SAAS,EAAE,IAAI;4CAErC,cAAc,IAAM,mBAAmB,SAAS,EAAE;4CAClD,cAAc,IAAM,mBAAmB;;8DAEvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAW,SAAS,IAAI;;;;;;sEACxC,6LAAC;sEAAM,SAAS,IAAI;;;;;;;;;;;;8DAEtB,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;2CAbnB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;kCAsB5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnFM;KAAA;uCAqFS", "debugId": null}}]}