import React from 'react'
import Link from 'next/link'
import { MAIN_CATEGORIES } from '@/constants'

const CategoriesGrid = () => {
  // Show only first 20 categories for the grid
  const displayCategories = MAIN_CATEGORIES.slice(0, 20)

  return (
    <div className="bg-white rounded-lg shadow-shopee p-6">
      <h2 className="text-xl font-semibold text-text-primary mb-6"><PERSON>h <PERSON></h2>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-10 gap-4">
        {displayCategories.map((category) => (
          <Link
            key={category.id}
            href={`/category/${category.slug}`}
            className="flex flex-col items-center p-3 rounded-lg hover:bg-secondary transition-colors group"
          >
            <div className="w-12 h-12 mb-2 flex items-center justify-center text-2xl bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg group-hover:from-primary/20 group-hover:to-accent/20 transition-colors">
              {category.icon}
            </div>
            <span className="text-xs text-center text-text-secondary group-hover:text-primary transition-colors leading-tight">
              {category.name}
            </span>
          </Link>
        ))}
      </div>
    </div>
  )
}

export default CategoriesGrid
