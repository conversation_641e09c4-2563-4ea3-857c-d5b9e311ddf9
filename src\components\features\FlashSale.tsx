'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronLeft, ChevronRight, Clock } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { formatPrice } from '@/lib/utils'

interface FlashSaleProduct {
  id: string
  name: string
  image: string
  originalPrice: number
  salePrice: number
  discount: number
  sold: number
  stock: number
}

// Mock flash sale products
const mockFlashSaleProducts: FlashSaleProduct[] = [
  {
    id: '1',
    name: '<PERSON><PERSON> thun nam basic',
    image: '/api/placeholder/200/200',
    originalPrice: 299000,
    salePrice: 99000,
    discount: 67,
    sold: 1234,
    stock: 500,
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON> sneaker nữ',
    image: '/api/placeholder/200/200',
    originalPrice: 899000,
    salePrice: 399000,
    discount: 56,
    sold: 567,
    stock: 200,
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON> x<PERSON>ch nữ cao cấp',
    image: '/api/placeholder/200/200',
    originalPrice: 1299000,
    salePrice: 649000,
    discount: 50,
    sold: 890,
    stock: 150,
  },
  {
    id: '4',
    name: '<PERSON><PERSON><PERSON> hồ nam thể thao',
    image: '/api/placeholder/200/200',
    originalPrice: 1999000,
    salePrice: 999000,
    discount: 50,
    sold: 234,
    stock: 100,
  },
  {
    id: '5',
    name: 'Tai nghe bluetooth',
    image: '/api/placeholder/200/200',
    originalPrice: 599000,
    salePrice: 199000,
    discount: 67,
    sold: 1567,
    stock: 300,
  },
  {
    id: '6',
    name: 'Ốp lưng iPhone',
    image: '/api/placeholder/200/200',
    originalPrice: 199000,
    salePrice: 49000,
    discount: 75,
    sold: 2345,
    stock: 1000,
  },
]

const FlashSale = () => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [timeLeft, setTimeLeft] = useState({
    hours: 2,
    minutes: 30,
    seconds: 45,
  })

  // Countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { hours: prev.hours - 1, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const itemsPerView = 6
  const maxIndex = Math.max(0, mockFlashSaleProducts.length - itemsPerView)

  const goToPrevious = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1))
  }

  const goToNext = () => {
    setCurrentIndex((prev) => Math.min(maxIndex, prev + 1))
  }

  return (
    <div className="bg-white rounded-lg shadow-shopee overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary to-accent p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-bold text-white">FLASH SALE</h2>
            <div className="flex items-center space-x-2 text-white">
              <Clock className="h-4 w-4" />
              <span className="text-sm">KẾT THÚC TRONG</span>
              <div className="flex space-x-1">
                <div className="bg-white text-primary px-2 py-1 rounded text-sm font-bold">
                  {String(timeLeft.hours).padStart(2, '0')}
                </div>
                <span className="text-white">:</span>
                <div className="bg-white text-primary px-2 py-1 rounded text-sm font-bold">
                  {String(timeLeft.minutes).padStart(2, '0')}
                </div>
                <span className="text-white">:</span>
                <div className="bg-white text-primary px-2 py-1 rounded text-sm font-bold">
                  {String(timeLeft.seconds).padStart(2, '0')}
                </div>
              </div>
            </div>
          </div>
          <Link 
            href="/flash-sale"
            className="text-white hover:text-gray-200 text-sm font-medium"
          >
            Xem tất cả
          </Link>
        </div>
      </div>

      {/* Products */}
      <div className="relative p-4">
        <div className="overflow-hidden">
          <div 
            className="flex transition-transform duration-300 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)` }}
          >
            {mockFlashSaleProducts.map((product) => (
              <div key={product.id} className="w-1/6 flex-shrink-0 px-2">
                <Link href={`/product/${product.id}`} className="block group">
                  <div className="border border-border rounded-lg overflow-hidden hover:shadow-shopee-md transition-shadow">
                    {/* Product Image */}
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                        <span className="text-gray-500 text-xs">Product Image</span>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-3">
                      <div className="flex items-center justify-center mb-2">
                        <span className="bg-primary text-white text-xs px-2 py-1 rounded">
                          -{product.discount}%
                        </span>
                      </div>
                      
                      <div className="text-center">
                        <div className="text-primary font-bold text-lg">
                          {formatPrice(product.salePrice)}
                        </div>
                        <div className="text-text-muted text-sm line-through">
                          {formatPrice(product.originalPrice)}
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mt-3">
                        <div className="bg-primary/20 rounded-full h-2 overflow-hidden">
                          <div 
                            className="bg-primary h-full rounded-full transition-all duration-300"
                            style={{ 
                              width: `${(product.sold / (product.sold + product.stock)) * 100}%` 
                            }}
                          />
                        </div>
                        <div className="text-xs text-text-muted text-center mt-1">
                          Đã bán {product.sold}
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Arrows */}
        {currentIndex > 0 && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 bg-white shadow-shopee-md"
            onClick={goToPrevious}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {currentIndex < maxIndex && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 bg-white shadow-shopee-md"
            onClick={goToNext}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}

export default FlashSale
